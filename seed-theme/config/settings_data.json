/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "current": {
    "show_accessibility": false,
    "enable_accessibility_default": false,
    "logo_width": 85,
    "logo_width_mobile": 70,
    "default_color_scheme": "scheme-6",
    "positive_vibes": "#64cd82",
    "negative_vibes": "#e93d3d",
    "buy_button_color": "#00a2f5",
    "buy_button_text_color": "#FFFFFF",
    "dynamic_buy_button_color": "#1e1f20",
    "preorder_button_color": "#1e1f20",
    "dropdown_color": "scheme-88f3b4e9-51a4-4e64-9eaa-01c66920e636",
    "price_color": "#1e1f20",
    "compare_at_price_color": "#938f9c",
    "product_label_color": "#1e1f20",
    "product_label_text_color": "#ffffff",
    "sale_label_color": "#e93d3d",
    "heading_font": "lato_n4",
    "body_font": "merriweather_n4",
    "h1_size": 150,
    "h2_size": 124,
    "body_font_size": 100,
    "prices_font_weight": "700",
    "h2_size_mobile": 80,
    "product_image_ratio": "none",
    "fill_product_images": true,
    "show_secondary_image": true,
    "multiply_product_images": "multiply",
    "multiply_collection_images": "multiply",
    "everything_rounded": true,
    "button_height": "size-l",
    "button_style": "plain",
    "button_rounded": "slightly-rounded",
    "button_font_weight": "200",
    "enable_cart_drawer_undo_remove": true,
    "enable_cart_drawer_undo_remove_delay": true,
    "cart_drawer_checkout_button": true,
    "enable_cart_drawer_upsell_complementary": true,
    "enable_cart_drawer_upsell_related": true,
    "trustbadge_image": "shop pay",
    "stock_label_qty": 5,
    "show_product_stock": false,
    "enable_color_swatches": true,
    "color_swatch_name": "Color stars",
    "enable_quick_buy_desktop": false,
    "enable_quick_buy_mobile": false,
    "enable_free_shipping": true,
    "free_shipping_amount": 50,
    "product_deliverytime_in_stock": "",
    "product_deliverytime_not_in_stock": "",
    "default_product_deliverytime_in_stock": "Order today = shipped today",
    "default_product_deliverytime_not_in_stock": "Delivery within 1 week",
    "banners_clickable": true,
    "checkout_accent_color": "#ff824c",
    "checkout_button_color": "#ff824c",
    "checkout_error_color": "#e93d3d",
    "sections": {
      "sticky-add-to-cart": {
        "type": "sticky-add-to-cart",
        "settings": {
          "enable": false,
          "position": "bottom",
          "color_palette": "",
          "button_style": "buy_button plain",
          "show_variant_picker": true,
          "show_unavailable_variants": true,
          "show_tax": false,
          "show_amount_selection": true,
          "preorder": false,
          "hide_in_theme_editor": false,
          "show_mobile": true
        }
      },
      "quiz-header": {
        "type": "quiz-header",
        "settings": {
          "show_top_bar": true,
          "top_bar_item_1": "180-day money-back guarantee",
          "top_bar_item_2": "Free shipping",
          "top_bar_item_3": "FSA/HSA eligible",
          "center_image_url": "https://dcpsvjmu5hpqr.cloudfront.net/images/2020-03/0bc35ca792c6f3d66bab7f35.webp",
          "back_text": "Back to home"
        }
      }
    },
    "content_for_index": [],
    "blocks": {
      "7570552984445421165": {
        "type": "shopify://apps/yotpo-product-reviews/blocks/settings/eb7dfd7d-db44-4334-bc49-c893b51b36cf",
        "disabled": false,
        "settings": {}
      },
      "855628211100114053": {
        "type": "shopify://apps/klaviyo-email-marketing-sms/blocks/klaviyo-onsite-embed/2632fe16-c075-4321-a88b-50b567f42507",
        "disabled": false,
        "settings": {}
      }
    },
    "color_schemes": {
      "scheme-1": {
        "settings": {
          "primary_bg": "#ffffff",
          "primary_bg_gradient": "",
          "secondary_bg": "#f4f4f4",
          "title_color": "#1e1f20",
          "title_color_gradient": "",
          "primary_fg": "#1e1f20",
          "primary_button_bg": "#71c68a",
          "primary_button_fg": "#ffffff",
          "secondary_button_bg": "#1e1f20",
          "secondary_button_fg": "#ffffff",
          "tertiary_button_bg": "#ffffff",
          "tertiary_button_fg": "#1e1f20",
          "input_bg": "#ffffff",
          "input_fg": "#1e1f20",
          "primary_bd": "#efefef",
          "accent": "#71c68a",
          "accent_gradient": "linear-gradient(320deg, rgba(232, 74, 147, 1) 4%, rgba(239, 179, 76, 1) 100%)"
        }
      },
      "scheme-2": {
        "settings": {
          "primary_bg": "#1e1f20",
          "primary_bg_gradient": "",
          "secondary_bg": "#8a8a9e",
          "title_color": "#ffffff",
          "title_color_gradient": "",
          "primary_fg": "#ffffff",
          "primary_button_bg": "#ffffff",
          "primary_button_fg": "#1e1f20",
          "secondary_button_bg": "#71c68a",
          "secondary_button_fg": "#ffffff",
          "tertiary_button_bg": "#1e1f20",
          "tertiary_button_fg": "#ffffff",
          "input_bg": "#ffffff",
          "input_fg": "#1e1f20",
          "primary_bd": "#fafafa",
          "accent": "#71c68a",
          "accent_gradient": ""
        }
      },
      "scheme-3": {
        "settings": {
          "primary_bg": "#ece8e2",
          "primary_bg_gradient": "",
          "secondary_bg": "#ffffff",
          "title_color": "#1e1f20",
          "title_color_gradient": "",
          "primary_fg": "#1e1f20",
          "primary_button_bg": "#f52a43",
          "primary_button_fg": "#ffffff",
          "secondary_button_bg": "#71c68a",
          "secondary_button_fg": "#ffffff",
          "tertiary_button_bg": "#ffffff",
          "tertiary_button_fg": "#1e1f20",
          "input_bg": "#ffffff",
          "input_fg": "#1e1f20",
          "primary_bd": "#ece8e2",
          "accent": "#71c68a",
          "accent_gradient": ""
        }
      },
      "scheme-4": {
        "settings": {
          "primary_bg": "#1e1f20",
          "primary_bg_gradient": "",
          "secondary_bg": "#ece8e2",
          "title_color": "#ffffff",
          "title_color_gradient": "",
          "primary_fg": "#ffffff",
          "primary_button_bg": "#71c68a",
          "primary_button_fg": "#ffffff",
          "secondary_button_bg": "#ffffff",
          "secondary_button_fg": "#1e1f20",
          "tertiary_button_bg": "#1e1f20",
          "tertiary_button_fg": "#ffffff",
          "input_bg": "#ffffff",
          "input_fg": "#1e1f20",
          "primary_bd": "#ece8e2",
          "accent": "#71c68a",
          "accent_gradient": ""
        }
      },
      "scheme-5": {
        "settings": {
          "primary_bg": "#71c68a",
          "primary_bg_gradient": "",
          "secondary_bg": "#f3f1ee",
          "title_color": "#ffffff",
          "title_color_gradient": "",
          "primary_fg": "#ffffff",
          "primary_button_bg": "#ffffff",
          "primary_button_fg": "#1e1f20",
          "secondary_button_bg": "#1e1f20",
          "secondary_button_fg": "#ffffff",
          "tertiary_button_bg": "#64cd82",
          "tertiary_button_fg": "#ffffff",
          "input_bg": "#ffffff",
          "input_fg": "#1e1f20",
          "primary_bd": "#ece8e2",
          "accent": "#ffffff",
          "accent_gradient": ""
        }
      },
      "scheme-6": {
        "settings": {
          "primary_bg": "#f7fafd",
          "primary_bg_gradient": "",
          "secondary_bg": "#f7fafd",
          "title_color": "#1e1f20",
          "title_color_gradient": "",
          "primary_fg": "#1e1f20",
          "primary_button_bg": "rgba(0,0,0,0)",
          "primary_button_fg": "#f4f4f4",
          "secondary_button_bg": "#1e1f20",
          "secondary_button_fg": "#ffffff",
          "tertiary_button_bg": "#ffffff",
          "tertiary_button_fg": "#1e1f20",
          "input_bg": "#fafafa",
          "input_fg": "#1e1f20",
          "primary_bd": "#ece8e2",
          "accent": "#71c68a",
          "accent_gradient": ""
        }
      },
      "scheme-7": {
        "settings": {
          "primary_bg": "#f3f1ee",
          "primary_bg_gradient": "linear-gradient(146deg, rgba(217, 221, 206, 1) 4%, rgba(255, 255, 255, 1) 99%)",
          "secondary_bg": "#71c68a",
          "title_color": "#1e1f20",
          "title_color_gradient": "",
          "primary_fg": "#1e1f20",
          "primary_button_bg": "#71c68a",
          "primary_button_fg": "#ffffff",
          "secondary_button_bg": "#1e1f20",
          "secondary_button_fg": "#ffffff",
          "tertiary_button_bg": "#ffffff",
          "tertiary_button_fg": "#1e1f20",
          "input_bg": "#ffffff",
          "input_fg": "#1e1f20",
          "primary_bd": "#ece8e2",
          "accent": "#71c68a",
          "accent_gradient": ""
        }
      },
      "scheme-8": {
        "settings": {
          "primary_bg": "#71c68a",
          "primary_bg_gradient": "linear-gradient(167deg, rgba(79, 158, 111, 1) 3%, rgba(112, 198, 137, 1) 100%)",
          "secondary_bg": "#f3f1ee",
          "title_color": "#ffffff",
          "title_color_gradient": "",
          "primary_fg": "#ffffff",
          "primary_button_bg": "#1e1f20",
          "primary_button_fg": "#ffffff",
          "secondary_button_bg": "#ffffff",
          "secondary_button_fg": "#1e1f20",
          "tertiary_button_bg": "#64cd82",
          "tertiary_button_fg": "#ffffff",
          "input_bg": "#ffffff",
          "input_fg": "#1e1f20",
          "primary_bd": "#ece8e2",
          "accent": "#71c68a",
          "accent_gradient": ""
        }
      },
      "scheme-608abf60-92b2-42f7-8e63-ea55f430d666": {
        "settings": {
          "primary_bg": "#f7fafd",
          "primary_bg_gradient": "",
          "secondary_bg": "#f4f4f4",
          "title_color": "#1e1f20",
          "title_color_gradient": "",
          "primary_fg": "#1e306e",
          "primary_button_bg": "#ffffff",
          "primary_button_fg": "#ffffff",
          "secondary_button_bg": "#1e1f20",
          "secondary_button_fg": "#ffffff",
          "tertiary_button_bg": "#ffffff",
          "tertiary_button_fg": "#1e1f20",
          "input_bg": "#ffffff",
          "input_fg": "#1e1f20",
          "primary_bd": "#efefef",
          "accent": "#8a8a9e",
          "accent_gradient": "linear-gradient(320deg, rgba(232, 74, 147, 1) 4%, rgba(239, 179, 76, 1) 100%)"
        }
      },
      "scheme-88f3b4e9-51a4-4e64-9eaa-01c66920e636": {
        "settings": {
          "primary_bg": "#f4f4f4",
          "primary_bg_gradient": "",
          "secondary_bg": "#f6f6f6",
          "title_color": "#4a4a4a",
          "title_color_gradient": "",
          "primary_fg": "#4a4a4a",
          "primary_button_bg": "#4a4a4a",
          "primary_button_fg": "#ffffff",
          "secondary_button_bg": "#4a4a4a",
          "secondary_button_fg": "#ffffff",
          "tertiary_button_bg": "#ffffff",
          "tertiary_button_fg": "#1e1f20",
          "input_bg": "#ffffff",
          "input_fg": "#1e1f20",
          "primary_bd": "#efefef",
          "accent": "#71c68a",
          "accent_gradient": "linear-gradient(320deg, rgba(232, 74, 147, 1) 4%, rgba(239, 179, 76, 1) 100%)"
        }
      },
      "scheme-32e4644b-fa95-4dd4-8163-bb8eaba2b78b": {
        "settings": {
          "primary_bg": "#ffffff",
          "primary_bg_gradient": "",
          "secondary_bg": "#f7fafd",
          "title_color": "#1e1f20",
          "title_color_gradient": "",
          "primary_fg": "#1e1f20",
          "primary_button_bg": "#f7fafd",
          "primary_button_fg": "#1e1f20",
          "secondary_button_bg": "#1e1f20",
          "secondary_button_fg": "#ffffff",
          "tertiary_button_bg": "#ffffff",
          "tertiary_button_fg": "#1e1f20",
          "input_bg": "#ffffff",
          "input_fg": "#1e1f20",
          "primary_bd": "#efefef",
          "accent": "#71c68a",
          "accent_gradient": "linear-gradient(320deg, rgba(232, 74, 147, 1) 4%, rgba(239, 179, 76, 1) 100%)"
        }
      }
    }
  },
  "installed_preset_name": "Seed",
  "presets": {
    "Xtra": {
      "enable_accessibility_default": false,
      "default_color_scheme": "scheme-1",
      "banners_clickable": true,
      "back_to_top_button": true,
      "logo_width": 85,
      "logo_width_mobile": 65,
      "width": 1280,
      "positive_vibes": "#4acb95",
      "negative_vibes": "#e93d3d",
      "buy_button_color": "#4acb95",
      "buy_button_text_color": "#FFFFFF",
      "dynamic_buy_button_color": "#000000",
      "checkout_button_style": "buy_button plain",
      "price_color": "#000000",
      "compare_at_price_color": "#939393",
      "product_label_color": "#000096",
      "product_label_text_color": "#ffffff",
      "sale_label_color": "#d83939",
      "sale_label_text_color": "#FFFFFF",
      "heading_font": "inter_n8",
      "body_font": "inter_n4",
      "h5_size": 100,
      "h6_size": 80,
      "body_font_size": 100,
      "base_font_size": 100,
      "h1_size_mobile": 100,
      "h2_size_mobile": 90,
      "h3_size_mobile": 100,
      "h4_size_mobile": 120,
      "h5_size_mobile": 100,
      "h6_size_mobile": 80,
      "product_image_ratio": "310x310",
      "fill_product_images": false,
      "show_secondary_image": true,
      "multiply_collection_images": "multiply-bg",
      "everything_rounded": true,
      "button_height": "size-m",
      "button_style": "solid",
      "button_rounded": "rounded",
      "button_font": "body",
      "enable_cart_drawer": true,
      "enable_cart_drawer_undo_remove": true,
      "enable_cart_drawer_undo_remove_delay": true,
      "cart_drawer_checkout_button": true,
      "enable_cart_drawer_upsell_complementary": true,
      "enable_cart_drawer_upsell_related": true,
      "enable_cart_drawer_upsell_variants": true,
      "show_trustbadge": true,
      "trustbadge_image": "shop pay",
      "productcards_text_alignment": "left",
      "stock_label_qty": 5,
      "show_product_stock": true,
      "show_product_stock_qty": 20,
      "enable_color_swatches": true,
      "enable_quick_buy_qty_selector": true,
      "enable_free_shipping": true,
      "free_shipping_amount": 50,
      "default_product_deliverytime_in_stock": "Next day delivery",
      "default_product_deliverytime_not_in_stock": "1-3 weeks",
      "default_product_deliverytime_info": "<p><strong>Mid weeks <br/></strong>Monday to friday we offer next day delivery if the product is on stock and exceptions on holidays. </p><p></p><p><strong>Weekends</strong></p><p>We offer no deliveries in the weekends. </p><p></p><p><strong>International </strong></p><p>Depending on where you’re located, we offer next day delivery. Check status on this <a href=\"/pages/shipping-info\" title=\"Shipping info\"><strong>link</strong></a></p>",
      "show_deliverytime_always": true,
      "shipping_timer_show_until": "23:59",
      "shipping_timer_show_from": "00",
      "shipping_timer_enable_6": true,
      "shipping_timer_enable_7": true,
      "checkout_logo_size": "small",
      "checkout_accent_color": "#ea558d",
      "checkout_button_color": "#64cd82",
      "checkout_error_color": "#e93d3d",
      "color_schemes": {
        "scheme-1": {
          "settings": {
            "primary_bg": "#ffffff",
            "primary_bg_gradient": "",
            "secondary_bg": "#f4f4f4",
            "title_color": "#000000",
            "title_color_gradient": "",
            "primary_fg": "#0b1613",
            "primary_button_bg": "#000096",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#fbea61",
            "secondary_button_fg": "#000000",
            "tertiary_button_bg": "#378cde",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#0b1613",
            "primary_bd": "#ecedec",
            "accent": "#f0702c",
            "accent_gradient": "linear-gradient(320deg, rgba(232, 74, 147, 1) 4%, rgba(239, 179, 76, 1) 100%)"
          }
        },
        "scheme-2": {
          "settings": {
            "primary_bg": "#0b1613",
            "primary_bg_gradient": "",
            "secondary_bg": "#8a8a9e",
            "title_color": "#ffffff",
            "title_color_gradient": "",
            "primary_fg": "#ffffff",
            "primary_button_bg": "#ffffff",
            "primary_button_fg": "#0b1613",
            "secondary_button_bg": "#fbea61",
            "secondary_button_fg": "#000000",
            "tertiary_button_bg": "#000096",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#0b1613",
            "primary_bd": "#fafafa",
            "accent": "#fbea61",
            "accent_gradient": ""
          }
        },
        "scheme-3": {
          "settings": {
            "primary_bg": "#fafafa",
            "primary_bg_gradient": "",
            "secondary_bg": "#ffffff",
            "title_color": "#0b1613",
            "title_color_gradient": "",
            "primary_fg": "#0b1613",
            "primary_button_bg": "#0b1613",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#f0702c",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#378cde",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#0b1613",
            "primary_bd": "#fafafa",
            "accent": "#f0702c",
            "accent_gradient": ""
          }
        },
        "scheme-4": {
          "settings": {
            "primary_bg": "#000096",
            "primary_bg_gradient": "",
            "secondary_bg": "#f4f4f4",
            "title_color": "#ffffff",
            "title_color_gradient": "",
            "primary_fg": "#ffffff",
            "primary_button_bg": "#fbea61",
            "primary_button_fg": "#000000",
            "secondary_button_bg": "#ffffff",
            "secondary_button_fg": "#0b1613",
            "tertiary_button_bg": "#ffffff",
            "tertiary_button_fg": "#0b1613",
            "input_bg": "#ffffff",
            "input_fg": "#0b1613",
            "primary_bd": "#fafafa",
            "accent": "#f0702c",
            "accent_gradient": ""
          }
        },
        "scheme-5": {
          "settings": {
            "primary_bg": "#f0702c",
            "primary_bg_gradient": "",
            "secondary_bg": "#ffffff",
            "title_color": "#ffffff",
            "title_color_gradient": "",
            "primary_fg": "#ffffff",
            "primary_button_bg": "#ffffff",
            "primary_button_fg": "#0b1613",
            "secondary_button_bg": "#0b1613",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#378cde",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#0b1613",
            "primary_bd": "#fafafa",
            "accent": "#ffffff",
            "accent_gradient": ""
          }
        },
        "scheme-6": {
          "settings": {
            "primary_bg": "#f4f4f4",
            "primary_bg_gradient": "linear-gradient(127deg, rgba(242, 247, 249, 1) 11%, rgba(233, 241, 244, 1) 81%)",
            "secondary_bg": "#ffffff",
            "title_color": "#0b1613",
            "title_color_gradient": "",
            "primary_fg": "#0b1613",
            "primary_button_bg": "#ffffff",
            "primary_button_fg": "#000000",
            "secondary_button_bg": "#fbea61",
            "secondary_button_fg": "#000000",
            "tertiary_button_bg": "#0b1613",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#0b1613",
            "primary_bd": "#fafafa",
            "accent": "#000096",
            "accent_gradient": ""
          }
        },
        "scheme-7": {
          "settings": {
            "primary_bg": "#d5f1ee",
            "primary_bg_gradient": "",
            "secondary_bg": "#f0702c",
            "title_color": "#0b1613",
            "title_color_gradient": "",
            "primary_fg": "#0b1613",
            "primary_button_bg": "#000096",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#fbea61",
            "secondary_button_fg": "#0b1613",
            "tertiary_button_bg": "#378cde",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#0b1613",
            "primary_bd": "#fafafa",
            "accent": "#000096",
            "accent_gradient": ""
          }
        },
        "scheme-8": {
          "settings": {
            "primary_bg": "#f0702c",
            "primary_bg_gradient": "linear-gradient(320deg, rgba(232, 74, 147, 1) 4%, rgba(239, 179, 76, 1) 100%)",
            "secondary_bg": "#f4f4f4",
            "title_color": "#ffffff",
            "title_color_gradient": "",
            "primary_fg": "#ffffff",
            "primary_button_bg": "#f0702c",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#ffffff",
            "secondary_button_fg": "#0b1613",
            "tertiary_button_bg": "#378cde",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#0b1613",
            "primary_bd": "#fafafa",
            "accent": "#f0702c",
            "accent_gradient": ""
          }
        }
      }
    },
    "Seed": {
      "enable_accessibility_default": false,
      "banners_clickable": true,
      "logo_width": 85,
      "logo_width_mobile": 70,
      "default_color_scheme": "scheme-6",
      "positive_vibes": "#64cd82",
      "negative_vibes": "#e93d3d",
      "buy_button_color": "#64cd82",
      "buy_button_text_color": "#FFFFFF",
      "dynamic_buy_button_color": "#1e1f20",
      "preorder_button_color": "#1e1f20",
      "dropdown_color": "scheme-1",
      "price_color": "#1e1f20",
      "compare_at_price_color": "#938f9c",
      "product_label_color": "#1e1f20",
      "product_label_text_color": "#ffffff",
      "sale_label_color": "#e93d3d",
      "heading_font": "instrument_sans_n7",
      "body_font": "instrument_sans_n4",
      "h1_size": 150,
      "h2_size": 124,
      "body_font_size": 100,
      "prices_font_weight": "700",
      "h2_size_mobile": 80,
      "product_image_ratio": "none",
      "fill_product_images": true,
      "show_secondary_image": true,
      "multiply_product_images": "multiply",
      "multiply_collection_images": "multiply",
      "everything_rounded": true,
      "button_height": "size-l",
      "button_style": "solid",
      "button_rounded": "slightly-rounded",
      "enable_cart_drawer_undo_remove": true,
      "enable_cart_drawer_undo_remove_delay": true,
      "cart_drawer_checkout_button": true,
      "enable_cart_drawer_upsell_complementary": true,
      "enable_cart_drawer_upsell_related": true,
      "trustbadge_image": "shop pay",
      "stock_label_qty": 5,
      "show_product_stock": false,
      "enable_quick_buy_desktop": false,
      "enable_quick_buy_mobile": false,
      "enable_free_shipping": true,
      "free_shipping_amount": 50,
      "product_deliverytime_in_stock": "",
      "product_deliverytime_not_in_stock": "",
      "default_product_deliverytime_in_stock": "Order today = shipped today",
      "default_product_deliverytime_not_in_stock": "Delivery within 1 week",
      "checkout_accent_color": "#ff824c",
      "checkout_button_color": "#ff824c",
      "checkout_error_color": "#e93d3d",
      "color_schemes": {
        "scheme-1": {
          "settings": {
            "primary_bg": "#ffffff",
            "primary_bg_gradient": "",
            "secondary_bg": "#f4f4f4",
            "title_color": "#1e1f20",
            "title_color_gradient": "",
            "primary_fg": "#1e1f20",
            "primary_button_bg": "#71c68a",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#1e1f20",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#ffffff",
            "tertiary_button_fg": "#1e1f20",
            "input_bg": "#ffffff",
            "input_fg": "#1e1f20",
            "primary_bd": "#efefef",
            "accent": "#71c68a",
            "accent_gradient": "linear-gradient(320deg, rgba(232, 74, 147, 1) 4%, rgba(239, 179, 76, 1) 100%)"
          }
        },
        "scheme-2": {
          "settings": {
            "primary_bg": "#1e1f20",
            "primary_bg_gradient": "",
            "secondary_bg": "#8a8a9e",
            "title_color": "#ffffff",
            "title_color_gradient": "",
            "primary_fg": "#ffffff",
            "primary_button_bg": "#ffffff",
            "primary_button_fg": "#1e1f20",
            "secondary_button_bg": "#71c68a",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#1e1f20",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#1e1f20",
            "primary_bd": "#fafafa",
            "accent": "#71c68a",
            "accent_gradient": ""
          }
        },
        "scheme-3": {
          "settings": {
            "primary_bg": "#ece8e2",
            "primary_bg_gradient": "",
            "secondary_bg": "#ffffff",
            "title_color": "#1e1f20",
            "title_color_gradient": "",
            "primary_fg": "#1e1f20",
            "primary_button_bg": "#1e1f20",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#71c68a",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#ffffff",
            "tertiary_button_fg": "#1e1f20",
            "input_bg": "#ffffff",
            "input_fg": "#1e1f20",
            "primary_bd": "#ece8e2",
            "accent": "#71c68a",
            "accent_gradient": ""
          }
        },
        "scheme-4": {
          "settings": {
            "primary_bg": "#1e1f20",
            "primary_bg_gradient": "",
            "secondary_bg": "#ece8e2",
            "title_color": "#ffffff",
            "title_color_gradient": "",
            "primary_fg": "#ffffff",
            "primary_button_bg": "#71c68a",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#ffffff",
            "secondary_button_fg": "#1e1f20",
            "tertiary_button_bg": "#1e1f20",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#1e1f20",
            "primary_bd": "#ece8e2",
            "accent": "#71c68a",
            "accent_gradient": ""
          }
        },
        "scheme-5": {
          "settings": {
            "primary_bg": "#71c68a",
            "primary_bg_gradient": "",
            "secondary_bg": "#f3f1ee",
            "title_color": "#ffffff",
            "title_color_gradient": "",
            "primary_fg": "#ffffff",
            "primary_button_bg": "#ffffff",
            "primary_button_fg": "#1e1f20",
            "secondary_button_bg": "#1e1f20",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#64cd82",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#1e1f20",
            "primary_bd": "#ece8e2",
            "accent": "#ffffff",
            "accent_gradient": ""
          }
        },
        "scheme-6": {
          "settings": {
            "primary_bg": "#f3f1ee",
            "primary_bg_gradient": "",
            "secondary_bg": "#ffffff",
            "title_color": "#1e1f20",
            "title_color_gradient": "",
            "primary_fg": "#1e1f20",
            "primary_button_bg": "#71c68a",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#1e1f20",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#ffffff",
            "tertiary_button_fg": "#1e1f20",
            "input_bg": "#ffffff",
            "input_fg": "#1e1f20",
            "primary_bd": "#ece8e2",
            "accent": "#71c68a",
            "accent_gradient": ""
          }
        },
        "scheme-7": {
          "settings": {
            "primary_bg": "#f3f1ee",
            "primary_bg_gradient": "linear-gradient(146deg, rgba(217, 221, 206, 1) 4%, rgba(255, 255, 255, 1) 99%)",
            "secondary_bg": "#71c68a",
            "title_color": "#1e1f20",
            "title_color_gradient": "",
            "primary_fg": "#1e1f20",
            "primary_button_bg": "#71c68a",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#1e1f20",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#ffffff",
            "tertiary_button_fg": "#1e1f20",
            "input_bg": "#ffffff",
            "input_fg": "#1e1f20",
            "primary_bd": "#ece8e2",
            "accent": "#71c68a",
            "accent_gradient": ""
          }
        },
        "scheme-8": {
          "settings": {
            "primary_bg": "#71c68a",
            "primary_bg_gradient": "linear-gradient(167deg, rgba(79, 158, 111, 1) 3%, rgba(112, 198, 137, 1) 100%)",
            "secondary_bg": "#f3f1ee",
            "title_color": "#ffffff",
            "title_color_gradient": "",
            "primary_fg": "#ffffff",
            "primary_button_bg": "#1e1f20",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#ffffff",
            "secondary_button_fg": "#1e1f20",
            "tertiary_button_bg": "#64cd82",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#1e1f20",
            "primary_bd": "#ece8e2",
            "accent": "#71c68a",
            "accent_gradient": ""
          }
        }
      }
    },
    "Calm": {
      "show_accessibility": false,
      "enable_accessibility_default": true,
      "default_color_scheme": "scheme-1",
      "banners_clickable": true,
      "back_to_top_button": true,
      "logo_width": 80,
      "logo_width_mobile": 70,
      "width": 2000,
      "positive_vibes": "#39c76a",
      "negative_vibes": "#a73d3d",
      "buy_button_color": "#1d1c1c",
      "buy_button_text_color": "#FFFFFF",
      "dynamic_buy_button_color": "#5e5e5e",
      "preorder_button_color": "#1d1c1c",
      "dropdown_color": "scheme-1",
      "price_color": "#000000",
      "compare_at_price_color": "#a1a1a1",
      "product_label_color": "#363636",
      "product_label_text_color": "#ffffff",
      "sale_label_color": "#e6261f",
      "heading_font": "proza_libre_n4",
      "body_font": "noto_sans_display_n4",
      "h1_size": 126,
      "h2_size": 120,
      "h4_size": 100,
      "h5_size": 110,
      "h6_size": 110,
      "heading_line_height": 120,
      "body_font_size": 108,
      "base_font_size": 104,
      "body_line_height": 160,
      "prices_font_weight": "400",
      "h1_size_mobile": 100,
      "h2_size_mobile": 90,
      "h3_size_mobile": 100,
      "h4_size_mobile": 98,
      "h5_size_mobile": 98,
      "h6_size_mobile": 98,
      "enable_hyphens": false,
      "product_image_ratio": "310x430",
      "fill_product_images": true,
      "show_secondary_image": true,
      "multiply_collection_images": "none",
      "everything_rounded": false,
      "button_height": "size-m",
      "button_style": "plain",
      "button_rounded": "square",
      "button_font": "body",
      "enable_cart_drawer_undo_remove": true,
      "enable_cart_drawer_undo_remove_delay": true,
      "cart_drawer_checkout_button": true,
      "enable_cart_drawer_upsell_complementary": true,
      "enable_cart_drawer_upsell_related": true,
      "enable_cart_drawer_upsell_variants": true,
      "show_trustbadge": true,
      "trustbadge_image": "shop pay",
      "search_drawer_show_vendor": true,
      "productcards_text_alignment": "left",
      "sale_label_price": "amount",
      "stock_label_qty": 3,
      "show_product_stock": true,
      "enable_color_swatches": true,
      "enable_quick_buy_desktop": false,
      "enable_quick_buy_mobile": false,
      "enable_free_shipping": true,
      "free_shipping_amount": 50,
      "default_product_deliverytime_in_stock": "Delivery in 1-3 days",
      "default_product_deliverytime_not_in_stock": "Not in stock - Pre order",
      "default_product_deliverytime_info": "<p><strong>Mid weeks <br/></strong>Monday to friday we offer next day delivery if the product is on stock and exceptions on holidays. </p><p></p><p><strong>Weekends</strong></p><p>We offer no deliveries in the weekends. </p><p></p><p><strong>International </strong></p><p>Depending on where you’re located, we offer next day delivery. Check status on this <a href=\"/pages/shipping-info\" title=\"Shipping info\"><strong>link</strong></a></p>",
      "checkout_logo_size": "small",
      "checkout_body_background_color": "#ffffff",
      "checkout_sidebar_background_color": "#f2f2f1",
      "checkout_accent_color": "#8ba4a9",
      "checkout_button_color": "#1d1c1c",
      "checkout_error_color": "#a73d3d",
      "color_schemes": {
        "scheme-1": {
          "settings": {
            "primary_bg": "#ffffff",
            "primary_bg_gradient": "",
            "secondary_bg": "#f2f2f1",
            "title_color": "#000000",
            "title_color_gradient": "",
            "primary_fg": "#656565",
            "primary_button_bg": "#000000",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#7e7e7e",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#585756",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#585756",
            "primary_bd": "#f2f2f1",
            "accent": "#7e7e7e",
            "accent_gradient": "linear-gradient(308deg, rgba(159, 108, 89, 1) 19%, rgba(230, 216, 176, 1) 84%)"
          }
        },
        "scheme-2": {
          "settings": {
            "primary_bg": "#000000",
            "primary_bg_gradient": "",
            "secondary_bg": "#7e7e7e",
            "title_color": "#ffffff",
            "title_color_gradient": "",
            "primary_fg": "#ffffff",
            "primary_button_bg": "#ffffff",
            "primary_button_fg": "#000000",
            "secondary_button_bg": "#7e7e7e",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#f4f2ed",
            "tertiary_button_fg": "#585756",
            "input_bg": "#ffffff",
            "input_fg": "#585756",
            "primary_bd": "#f2f2f1",
            "accent": "#7e7e7e",
            "accent_gradient": ""
          }
        },
        "scheme-3": {
          "settings": {
            "primary_bg": "#f2f2f1",
            "primary_bg_gradient": "",
            "secondary_bg": "#ffffff",
            "title_color": "#1d1c1c",
            "title_color_gradient": "",
            "primary_fg": "#585756",
            "primary_button_bg": "#1d1c1c",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#7e7e7e",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#ffffff",
            "tertiary_button_fg": "#585756",
            "input_bg": "#ffffff",
            "input_fg": "#0b1613",
            "primary_bd": "#f2f2f1",
            "accent": "#7e7e7e",
            "accent_gradient": ""
          }
        },
        "scheme-4": {
          "settings": {
            "primary_bg": "#585756",
            "primary_bg_gradient": "",
            "secondary_bg": "#f2f2f1",
            "title_color": "#ffffff",
            "title_color_gradient": "",
            "primary_fg": "#ffffff",
            "primary_button_bg": "#ffffff",
            "primary_button_fg": "#585756",
            "secondary_button_bg": "#1d1c1c",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#f4f2ed",
            "tertiary_button_fg": "#1d1c1c",
            "input_bg": "#ffffff",
            "input_fg": "#585756",
            "primary_bd": "#f2f2f1",
            "accent": "#ffffff",
            "accent_gradient": ""
          }
        },
        "scheme-5": {
          "settings": {
            "primary_bg": "#7e7e7e",
            "primary_bg_gradient": "",
            "secondary_bg": "#f4f2ed",
            "title_color": "#ffffff",
            "title_color_gradient": "",
            "primary_fg": "#ffffff",
            "primary_button_bg": "#ffffff",
            "primary_button_fg": "#1d1c1c",
            "secondary_button_bg": "#1d1c1c",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#7e7e7e",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#585756",
            "primary_bd": "#fafafa",
            "accent": "#ffffff",
            "accent_gradient": ""
          }
        },
        "scheme-6": {
          "settings": {
            "primary_bg": "#f4f2ec",
            "primary_bg_gradient": "",
            "secondary_bg": "#ffffff",
            "title_color": "#000000",
            "title_color_gradient": "",
            "primary_fg": "#656565",
            "primary_button_bg": "#000000",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#1d1c1c",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#ffffff",
            "tertiary_button_fg": "#1d1c1c",
            "input_bg": "#ffffff",
            "input_fg": "#585756",
            "primary_bd": "#f2f2f1",
            "accent": "#7e7e7e",
            "accent_gradient": "linear-gradient(127deg, rgba(159, 108, 89, 1) 11%, rgba(230, 216, 176, 1) 81%)"
          }
        },
        "scheme-7": {
          "settings": {
            "primary_bg": "#f4f2ed",
            "primary_bg_gradient": "linear-gradient(120deg, rgba(236, 243, 244, 1), rgba(244, 242, 237, 1) 100%)",
            "secondary_bg": "#7e7e7e",
            "title_color": "#1d1c1c",
            "title_color_gradient": "",
            "primary_fg": "#1d1c1c",
            "primary_button_bg": "#7e7e7e",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#1d1c1c",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#585756",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#1d1c1c",
            "primary_bd": "#f2f2f1",
            "accent": "#7e7e7e",
            "accent_gradient": ""
          }
        },
        "scheme-8": {
          "settings": {
            "primary_bg": "#7e7e7e",
            "primary_bg_gradient": "linear-gradient(341deg, rgba(159, 108, 89, 1) 19%, rgba(230, 216, 176, 1) 100%)",
            "secondary_bg": "#f4f2ed",
            "title_color": "#ffffff",
            "title_color_gradient": "",
            "primary_fg": "#ffffff",
            "primary_button_bg": "#ffffff",
            "primary_button_fg": "#1d1c1c",
            "secondary_button_bg": "#1d1c1c",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#7e7e7e",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#585756",
            "primary_bd": "#f2f2f1",
            "accent": "#ffffff",
            "accent_gradient": ""
          }
        }
      }
    },
    "Vault": {
      "enable_accessibility_default": false,
      "banners_clickable": true,
      "width": 1280,
      "default_color_scheme": "scheme-1",
      "back_to_top_button": true,
      "logo_width": 90,
      "logo_width_mobile": 60,
      "positive_vibes": "#95bf47",
      "negative_vibes": "#e93d3d",
      "buy_button_color": "#2c374d",
      "buy_button_text_color": "#ffffff",
      "dynamic_buy_button_color": "#121723",
      "price_color": "#2c374d",
      "compare_at_price_color": "#808793",
      "product_label_color": "#3ab0d8",
      "product_label_text_color": "#ffffff",
      "sale_label_color": "#e93d3d",
      "sale_label_text_color": "#ffffff",
      "heading_font": "figtree_n7",
      "body_font": "sans-serif",
      "h1_size": 100,
      "body_font_size": 100,
      "base_font_size": 100,
      "h1_size_mobile": 82,
      "h2_size_mobile": 94,
      "h3_size_mobile": 110,
      "h4_size_mobile": 138,
      "h5_size_mobile": 164,
      "h6_size_mobile": 158,
      "product_image_ratio": "310x310",
      "fill_product_images": false,
      "multiply_collection_images": "none",
      "everything_rounded": true,
      "button_height": "size-s",
      "button_style": "plain",
      "button_rounded": "slightly-rounded",
      "button_font": "body",
      "enable_cart_drawer_undo_remove": true,
      "enable_cart_drawer_undo_remove_delay": true,
      "cart_drawer_checkout_button": true,
      "enable_cart_drawer_upsell_complementary": true,
      "enable_cart_drawer_upsell_related": true,
      "enable_cart_drawer_upsell_variants": true,
      "show_trustbadge": true,
      "trustbadge_image": "shop pay",
      "show_sale_label": true,
      "show_stock_label": true,
      "stock_label_qty": 5,
      "show_product_stock": true,
      "enable_color_swatches": true,
      "enable_quick_buy_desktop": true,
      "enable_quick_buy_mobile": true,
      "enable_quick_buy_qty_selector": true,
      "enable_free_shipping": true,
      "free_shipping_amount": 50,
      "default_product_deliverytime_in_stock": "Delivery within 48 hours, pay afterwards",
      "default_product_deliverytime_info": "<p><strong>Mid weeks</strong></p><p>Monday to friday we offer next day delivery if the product is on stock and exceptions on holidays.<br/><br/><strong>Weekends</strong></p><p>We offer no deliveries in the weekends.<br/><br/><strong>International</strong></p><p>Depending on where you’re located, we offer next day delivery. Check your status on this <a href=\"/pages/shipping-info\" title=\"Shipping info\"><strong>link</strong></a></p>",
      "show_deliverytime_always": true,
      "shipping_timer_show_from": "07:00:00",
      "shipping_timer_enable_3": true,
      "checkout_logo_size": "small",
      "checkout_sidebar_background_color": "#eef5f7",
      "checkout_accent_color": "#3ab0d8",
      "checkout_button_color": "#2c374d",
      "checkout_error_color": "#e93d3d",
      "color_schemes": {
        "scheme-1": {
          "settings": {
            "primary_bg": "#ffffff",
            "primary_bg_gradient": "",
            "secondary_bg": "#eef5f7",
            "title_color": "#121723",
            "title_color_gradient": "",
            "primary_fg": "#121723",
            "primary_button_bg": "#3a34d2",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#121723",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#2c374d",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#2c374d",
            "primary_bd": "#e5e5e5",
            "accent": "#3a34d2",
            "accent_gradient": "linear-gradient(320deg, rgba(245, 215, 73, 1) 4%, rgba(145, 212, 234, 1) 100%)"
          }
        },
        "scheme-2": {
          "settings": {
            "primary_bg": "#110744",
            "primary_bg_gradient": "",
            "secondary_bg": "#f7f7f7",
            "title_color": "#ffffff",
            "title_color_gradient": "",
            "primary_fg": "#ffffff",
            "primary_button_bg": "#ffffff",
            "primary_button_fg": "#110744",
            "secondary_button_bg": "#d8fd80",
            "secondary_button_fg": "#110744",
            "tertiary_button_bg": "#3a34d2",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#121723",
            "primary_bd": "#f7f7f7",
            "accent": "#d8fd80",
            "accent_gradient": ""
          }
        },
        "scheme-3": {
          "settings": {
            "primary_bg": "#f4f5f5",
            "primary_bg_gradient": "",
            "secondary_bg": "#ffffff",
            "title_color": "#121723",
            "title_color_gradient": "",
            "primary_fg": "#121723",
            "primary_button_bg": "#121723",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#3a34d2",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#2c374d",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#2c374d",
            "primary_bd": "#f7f7f7",
            "accent": "#3ab0d8",
            "accent_gradient": ""
          }
        },
        "scheme-4": {
          "settings": {
            "primary_bg": "#0c0530",
            "primary_bg_gradient": "",
            "secondary_bg": "#f4f4f4",
            "title_color": "#ffffff",
            "title_color_gradient": "",
            "primary_fg": "#ffffff",
            "primary_button_bg": "#ffffff",
            "primary_button_fg": "#2c374d",
            "secondary_button_bg": "#ffffff",
            "secondary_button_fg": "#121723",
            "tertiary_button_bg": "#3ab0d8",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#2c374d",
            "primary_bd": "#f7f7f7",
            "accent": "#3ab0d8",
            "accent_gradient": ""
          }
        },
        "scheme-5": {
          "settings": {
            "primary_bg": "#3a34d2",
            "primary_bg_gradient": "",
            "secondary_bg": "#ffffff",
            "title_color": "#ffffff",
            "title_color_gradient": "",
            "primary_fg": "#ffffff",
            "primary_button_bg": "#ffffff",
            "primary_button_fg": "#2c374d",
            "secondary_button_bg": "#2c374d",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#ffffff",
            "tertiary_button_fg": "#2c374d",
            "input_bg": "#ffffff",
            "input_fg": "#2c374d",
            "primary_bd": "#f7f7f7",
            "accent": "#ffffff",
            "accent_gradient": ""
          }
        },
        "scheme-6": {
          "settings": {
            "primary_bg": "#d9fd81",
            "primary_bg_gradient": "",
            "secondary_bg": "#ffffff",
            "title_color": "#121723",
            "title_color_gradient": "",
            "primary_fg": "#121723",
            "primary_button_bg": "#ffffff",
            "primary_button_fg": "#121723",
            "secondary_button_bg": "#121723",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#121723",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#2c374d",
            "primary_bd": "#f7f7f7",
            "accent": "#3a34d2",
            "accent_gradient": ""
          }
        },
        "scheme-7": {
          "settings": {
            "primary_bg": "#eef5f7",
            "primary_bg_gradient": "linear-gradient(120deg, rgba(145, 212, 234, 0.3), rgba(245, 215, 73, 0.2) 100%)",
            "secondary_bg": "#3ab0d8",
            "title_color": "#121723",
            "title_color_gradient": "",
            "primary_fg": "#2c374d",
            "primary_button_bg": "#3ab0d8",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#121723",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#2c374d",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#2c374d",
            "primary_bd": "#f7f7f7",
            "accent": "#3ab0d8",
            "accent_gradient": ""
          }
        },
        "scheme-8": {
          "settings": {
            "primary_bg": "#3ab0d8",
            "primary_bg_gradient": "linear-gradient(320deg, rgba(245, 215, 73, 1) 4%, rgba(145, 212, 234, 1) 100%)",
            "secondary_bg": "#eef5f7",
            "title_color": "#ffffff",
            "title_color_gradient": "",
            "primary_fg": "#ffffff",
            "primary_button_bg": "#3ab0d8",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#ffffff",
            "secondary_button_fg": "#2c374d",
            "tertiary_button_bg": "#121723",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#2c374d",
            "primary_bd": "#f7f7f7",
            "accent": "#3ab0d8",
            "accent_gradient": ""
          }
        }
      }
    },
    "Rover": {
      "enable_accessibility_default": false,
      "back_to_top_button": true,
      "logo_width": 125,
      "logo_width_mobile": 90,
      "default_color_scheme": "scheme-1",
      "positive_vibes": "#a7be48",
      "negative_vibes": "#e93d3d",
      "buy_button_color": "#a7be48",
      "buy_button_text_color": "#FFFFFF",
      "dynamic_buy_button_color": "#353535",
      "preorder_button_color": "#f3cb41",
      "checkout_button_style": "buy_button plain",
      "price_color": "#353535",
      "compare_at_price_color": "#939393",
      "product_label_color": "#e3bd33",
      "product_label_text_color": "#ffffff",
      "sale_label_color": "#d83939",
      "sale_label_text_color": "#FFFFFF",
      "heading_font": "montserrat_n8",
      "body_font": "inter_n4",
      "h1_size": 128,
      "h2_size": 120,
      "h3_size": 106,
      "h4_size": 106,
      "h5_size": 112,
      "h6_size": 80,
      "heading_line_height": 115,
      "primary_letter_spacing": -3,
      "global_title_size": "h4",
      "body_font_size": 96,
      "base_font_size": 98,
      "prices_font_weight": "700",
      "drawers_font_size_heading": "h3",
      "enable_hyphens": false,
      "h1_size_mobile": 94,
      "h2_size_mobile": 90,
      "h3_size_mobile": 94,
      "h4_size_mobile": 98,
      "h5_size_mobile": 80,
      "h6_size_mobile": 80,
      "body_font_size_mobile": "13px",
      "product_image_ratio": "310x310",
      "fill_product_images": false,
      "show_secondary_image": true,
      "multiply_product_images": "multiply-bg",
      "multiply_product_images_color_palette": "scheme-3",
      "multiply_collection_images": "multiply-bg",
      "everything_rounded": true,
      "button_height": "size-s",
      "button_style": "plain",
      "button_rounded": "slightly-rounded",
      "button_font": "body",
      "search_drawer_show_vendor": true,
      "stock_label_qty": 5,
      "product_custom_label": "",
      "show_product_stock": true,
      "product_short_description": "product_description",
      "enable_quick_buy_desktop": true,
      "enable_quick_buy_mobile": true,
      "enable_quick_buy_qty_selector": true,
      "enable_free_shipping": false,
      "checkout_logo_size": "small",
      "checkout_accent_color": "#ea558d",
      "checkout_button_color": "#64cd82",
      "checkout_error_color": "#e93d3d",
      "color_schemes": {
        "scheme-1": {
          "settings": {
            "primary_bg": "#ffffff",
            "primary_bg_gradient": "",
            "secondary_bg": "#f2f2f2",
            "title_color": "#1a3437",
            "title_color_gradient": "",
            "primary_fg": "#1a3437",
            "primary_button_bg": "#e3bd33",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#1a3437",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#378cde",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#1a3437",
            "primary_bd": "#e4e4e4",
            "accent": "#e3bd33",
            "accent_gradient": "linear-gradient(320deg, rgba(232, 74, 147, 1) 4%, rgba(239, 179, 76, 1) 100%)"
          }
        },
        "scheme-2": {
          "settings": {
            "primary_bg": "#1a3437",
            "primary_bg_gradient": "",
            "secondary_bg": "#8a8a9e",
            "title_color": "#ffffff",
            "title_color_gradient": "",
            "primary_fg": "#ffffff",
            "primary_button_bg": "#ffffff",
            "primary_button_fg": "#1a3437",
            "secondary_button_bg": "#e3bd33",
            "secondary_button_fg": "#1a3437",
            "tertiary_button_bg": "#a7be48",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#1a3437",
            "primary_bd": "#fafafa",
            "accent": "#ffe271",
            "accent_gradient": ""
          }
        },
        "scheme-3": {
          "settings": {
            "primary_bg": "#efefef",
            "primary_bg_gradient": "linear-gradient(180deg, rgba(247, 247, 247, 1) 68%, rgba(243, 243, 243, 1) 99%)",
            "secondary_bg": "#ffffff",
            "title_color": "#1a3437",
            "title_color_gradient": "",
            "primary_fg": "#1a3437",
            "primary_button_bg": "#1a3437",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#e3bd33",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#e6e6e6",
            "tertiary_button_fg": "#1a3437",
            "input_bg": "#ffffff",
            "input_fg": "#1a3437",
            "primary_bd": "#fafafa",
            "accent": "#e3bd33",
            "accent_gradient": ""
          }
        },
        "scheme-4": {
          "settings": {
            "primary_bg": "#e3bd33",
            "primary_bg_gradient": "",
            "secondary_bg": "#f4f4f4",
            "title_color": "#ffffff",
            "title_color_gradient": "",
            "primary_fg": "#ffffff",
            "primary_button_bg": "#ffffff",
            "primary_button_fg": "#1a3437",
            "secondary_button_bg": "#ffffff",
            "secondary_button_fg": "#1a3437",
            "tertiary_button_bg": "#d2a80c",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#1a3437",
            "primary_bd": "#fafafa",
            "accent": "#d2a80c",
            "accent_gradient": ""
          }
        },
        "scheme-5": {
          "settings": {
            "primary_bg": "#f0702c",
            "primary_bg_gradient": "",
            "secondary_bg": "#ffffff",
            "title_color": "#ffffff",
            "title_color_gradient": "",
            "primary_fg": "#ffffff",
            "primary_button_bg": "#ffffff",
            "primary_button_fg": "#0b1613",
            "secondary_button_bg": "#0b1613",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#378cde",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#0b1613",
            "primary_bd": "#fafafa",
            "accent": "#ffffff",
            "accent_gradient": ""
          }
        },
        "scheme-6": {
          "settings": {
            "primary_bg": "#f4f4f4",
            "primary_bg_gradient": "linear-gradient(127deg, rgba(242, 247, 249, 1) 11%, rgba(233, 241, 244, 1) 81%)",
            "secondary_bg": "#ffffff",
            "title_color": "#1a3437",
            "title_color_gradient": "",
            "primary_fg": "#1a3437",
            "primary_button_bg": "#ffffff",
            "primary_button_fg": "#1a3437",
            "secondary_button_bg": "#d2a80c",
            "secondary_button_fg": "#ffffff",
            "tertiary_button_bg": "#1a3437",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#1a3437",
            "primary_bd": "#fafafa",
            "accent": "#d2a80c",
            "accent_gradient": ""
          }
        },
        "scheme-7": {
          "settings": {
            "primary_bg": "#fdf7e1",
            "primary_bg_gradient": "",
            "secondary_bg": "#d2a80c",
            "title_color": "#1a3437",
            "title_color_gradient": "",
            "primary_fg": "#1a3437",
            "primary_button_bg": "#1a3437",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#ffffff",
            "secondary_button_fg": "#1a3437",
            "tertiary_button_bg": "#d2a80c",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#1a3437",
            "primary_bd": "#fafafa",
            "accent": "#e3bd33",
            "accent_gradient": ""
          }
        },
        "scheme-8": {
          "settings": {
            "primary_bg": "#225699",
            "primary_bg_gradient": "linear-gradient(180deg, rgba(26, 52, 55, 0.17) 3%, rgba(5, 10, 10, 1) 85%)",
            "secondary_bg": "#f4f4f4",
            "title_color": "#ffffff",
            "title_color_gradient": "",
            "primary_fg": "#ffffff",
            "primary_button_bg": "#f0702c",
            "primary_button_fg": "#ffffff",
            "secondary_button_bg": "#ffffff",
            "secondary_button_fg": "#0b1613",
            "tertiary_button_bg": "#378cde",
            "tertiary_button_fg": "#ffffff",
            "input_bg": "#ffffff",
            "input_fg": "#0b1613",
            "primary_bd": "#fafafa",
            "accent": "#f0702c",
            "accent_gradient": ""
          }
        }
      }
    }
  }
}
